import React, { useState, useEffect } from 'react';
import PayrollSlabsServices from '@/services/PayrollSlabs/PayrollSlabs';
import { useNavigate } from 'react-router-dom';
import useEmpDataStore from '@/store/empDataStore';
import { 
    notification, 
    TableService, 
    ConfirmationService 
}from '@/services';

const PayrollSlabs = () => {
    const {
        currentEmployee,
        fetchEmployeeById,
        getEmployeeDisplayName,
        loading,
        error
    } = useEmpDataStore();

    const [slabs, setSlabs] = useState([]);
    const navigate = useNavigate();

    useEffect(() => {
        const loadSlabs = async () => {
            const data = await PayrollSlabsServices.getAllSlabs();
            if (data) setSlabs(data);
        };

        loadSlabs();
    }, []);

    return (
        <div>PayrollSlabs</div>
    );
};

export default PayrollSlabs;
